## PhoneReader
Get information of any phone number. 

A simple but efficient OSINT tool in python3 for extracting information.

## Usage
```css
  1. apt install python3
  2. git clone https://github.com/victorpreston/PhoneTracker.git
  3. cd Phone_Reader
  4. ls
  5. pip install -r requirements.txt
  6. python3 locator.py
```
## Screenshot

![Screenshot from 2023-07-10 02-05-33](https://github.com/victorpreston/PhoneTracker/assets/112781610/5dd00270-ef71-4027-8dba-62c69a6a40b1)


## *Try To*
```css
    1. You must add country code with +
    2. Type the number without any space
```

## Help
  `Try to raise an issue first`
   `You can also start a PR`
   
Don't Forget To ⭐
